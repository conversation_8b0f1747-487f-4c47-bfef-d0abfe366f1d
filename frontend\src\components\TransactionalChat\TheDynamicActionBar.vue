<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue'
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore'
import { useI18n } from 'vue-i18n'
import { useIntersectionObserver } from '@/composables/useIntersectionObserver'

const { t } = useI18n()
const transactionalChatStore = useTransactionalChatStore()

// Element reference for intersection observer
const dynamicActionBarRef = ref<HTMLElement | null>(null)

// Local state for UI management
const mode = ref<'chat' | 'action'>('action') // Start with action mode for smart steps
const messageInput = ref<string>('')
const inputElement = ref<HTMLInputElement>()
const isSending = ref<boolean>(false)
const showConfirmation = ref<boolean>(false)
const confirmationAction = ref<string>('')

// Computed properties
const currentStep = computed(() => transactionalChatStore.currentStep)
const isUsersTurn = computed(() => transactionalChatStore.isUsersTurn)

// Determine which mode to show based on current step and user turn
const shouldShowActionMode = computed(() => {
  const step = currentStep.value
  const userTurn = isUsersTurn.value
  
  console.log('Dynamic Action Bar Debug:', {
    stepKey: step?.key,
    isUsersTurn: userTurn,
    shouldShow: step && userTurn && (step.key === 'confirmReceipt' || step.key === 'yourTurnToPay')
  })
  
  if (!step || !userTurn) return false
  
  // Step 4 (confirmReceipt) and Step 5 (yourTurnToPay) are the key steps that need the smart action bar
  return step.key === 'confirmReceipt' || step.key === 'yourTurnToPay'
})

// Track visibility of this dynamic action bar
const { observe, disconnect } = useIntersectionObserver((isVisible) => {
  // Only update store if this bar is showing actions AND there's a pinned action for a dynamic action step
  const step = currentStep.value;
  const pinnedAction = transactionalChatStore.pinnedAction;
  const isDynamicActionStep = step && (step.key === 'confirmReceipt' || step.key === 'makeSecondPayment' || step.key === 'confirmFirstPaymentReceipt');
  
  if (shouldShowActionMode.value && isDynamicActionStep && pinnedAction?.cardId === `dynamic-action-${step.key}`) {
    transactionalChatStore.setActionCardVisibility(isVisible);
  }
});

// Set up observer when component mounts and when action mode changes
onMounted(() => {
  if (dynamicActionBarRef.value) {
    observe(dynamicActionBarRef.value);
  }
});

// Watch for changes in action mode to update visibility tracking
watch(shouldShowActionMode, (newValue) => {
  // Disconnect previous observer to prevent memory leaks
  disconnect();
  
  // Only set up new observer if we need action mode and have element reference
  if (newValue && dynamicActionBarRef.value) {
    observe(dynamicActionBarRef.value);
  }
});

// Clean up observer when component is unmounted
onUnmounted(() => {
  disconnect();
});

const shouldShowChatIcon = computed(() => {
  // Only show chat icon for step 4 (confirmReceipt)
  // Step 5 (yourTurnToPay) should not show chat icon as per requirements
  return currentStep.value?.key === 'confirmReceipt'
})

const actionButtonText = computed(() => {
  const step = currentStep.value
  if (!step) return ''
  
  switch (step.key) {
    case 'confirmReceipt':
      return t('transactionalChat.actionCards.confirmReceipt.button')
    case 'yourTurnToPay':
      return t('transactionalChat.actionCards.yourTurnToPay.button')
    default:
      return 'Action'
  }
})

const actionButtonClass = computed(() => {
  const step = currentStep.value
  const baseClass = 'action-button'
  
  if (step?.key === 'confirmReceipt') {
    return `${baseClass} danger`
  }
  
  return baseClass
})


// Methods
const switchToChat = async () => {
  mode.value = 'chat'
  // Focus will be handled by the watcher
}

const handleSendMessage = async () => {
  const message = messageInput.value.trim()
  if (!message || isSending.value) return

  isSending.value = true

  try {
    await transactionalChatStore.sendMessage(message)
    messageInput.value = ''

    // Switch back to action mode after sending message (for smart steps)
    if (shouldShowActionMode.value) {
      mode.value = 'action'
    } else {
      // If still in chat mode, refocus the input for continuous typing
      // Mobile-optimized focus strategy
      nextTick(() => {
        // Single attempt with proper mobile handling
        if (inputElement.value && mode.value === 'chat') {
          // On mobile, focus must be triggered by user interaction
          // Use a small delay to ensure DOM is ready
          setTimeout(() => {
            inputElement.value?.focus()
            // Mobile Safari sometimes needs this
            inputElement.value?.click()
          }, 50)
        }
      })
    }
  } catch (error) {
    console.error('Failed to send message:', error)
  } finally {
    isSending.value = false
  }
}

// Watch for mode changes to 'chat' - mobile optimized
watch(mode, (newMode) => {
  if (newMode === 'chat') {
    nextTick(() => {
      // Single focus attempt with mobile-friendly timing
      setTimeout(() => {
        if (inputElement.value) {
          inputElement.value.focus()
          // For mobile Safari which sometimes needs click event
          inputElement.value.click()
        }
      }, 100)
    })
  }
})

// Watch for template condition changes - simplified for mobile
watch(() => !shouldShowActionMode.value || mode.value === 'chat', (shouldShowChat) => {
  if (shouldShowChat && mode.value === 'chat') {
    nextTick(() => {
      setTimeout(() => {
        if (inputElement.value) {
          inputElement.value.focus()
        }
      }, 100)
    })
  }
})

const handleKeyPress = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    handleSendMessage()
  }
}

// Mobile touch event handlers to prevent scroll interference
let touchStartY = 0
let touchStartX = 0
let isInputFocused = false

const handleTouchStart = (event: TouchEvent) => {
  // Record initial touch position
  const touch = event.touches[0]
  touchStartY = touch.clientY
  touchStartX = touch.clientX

  // Check if the input is focused
  isInputFocused = document.activeElement === inputElement.value

  // Allow normal touch behavior on the input field itself
  // but prevent scroll bubbling to parent containers
  event.stopPropagation()
}

const handleTouchMove = (event: TouchEvent) => {
  const touch = event.touches[0]
  const deltaY = Math.abs(touch.clientY - touchStartY)
  const deltaX = Math.abs(touch.clientX - touchStartX)

  // If this is a vertical scroll gesture (more vertical than horizontal movement)
  // and the input is focused, prevent it from bubbling up
  if (deltaY > deltaX && deltaY > 10) {
    // This is likely a scroll gesture, prevent it from affecting the page
    event.preventDefault()
    event.stopPropagation()
  } else {
    // Allow horizontal gestures and small movements (for text selection, etc.)
    event.stopPropagation()
  }
}

const handleTouchEnd = (event: TouchEvent) => {
  // Clean up touch event handling
  event.stopPropagation()
  // Reset tracking variables
  touchStartY = 0
  touchStartX = 0
}

// Input focus handlers for better mobile experience
const handleInputFocus = () => {
  isInputFocused = true
}

const handleInputBlur = () => {
  isInputFocused = false
}

const handleActionClick = () => {
  const step = currentStep.value
  if (!step) return
  
  // For sensitive actions, show confirmation
  if (step.key === 'confirmReceipt' || step.key === 'yourTurnToPay') {
    confirmationAction.value = step.key
    showConfirmation.value = true
  } else {
    performAction(step.key)
  }
}

const confirmAction = () => {
  performAction(confirmationAction.value)
  showConfirmation.value = false
  confirmationAction.value = ''
}

const cancelAction = () => {
  showConfirmation.value = false
  confirmationAction.value = ''
}

const performAction = async (actionType: string) => {
  try {
    await transactionalChatStore.performAction(actionType)
  } catch (error) {
    console.error('Failed to perform action:', error)
  }
}

const getConfirmationMessage = computed(() => {
  switch (confirmationAction.value) {
    case 'confirmReceipt':
      return t('transactionalChat.confirmations.confirmReceipt')
    case 'yourTurnToPay':
      return t('transactionalChat.confirmations.sendPayment')
    default:
      return 'Are you sure?'
  }
})
</script>

<template>
  <div 
    ref="dynamicActionBarRef"
    class="dynamic-action-bar"
    data-testid="dynamic-action-bar"
  >
    <!-- Standard Chat Mode (default) -->
    <div 
      v-if="!shouldShowActionMode || mode === 'chat'"
      class="chat-mode"
      data-testid="chat-mode"
    >
      <div
        class="chat-input-container"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <input
          ref="inputElement"
          v-model="messageInput"
          type="text"
          class="chat-input"
          :placeholder="t('transactionalChat.chat.placeholder')"
          data-testid="chat-input"
          @keypress="handleKeyPress"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
          @focus="handleInputFocus"
          @blur="handleInputBlur"
          :disabled="isSending"
          autocomplete="off"
          autocorrect="off"
          autocapitalize="off"
          spellcheck="false"
          inputmode="text"
        />
        <button 
          class="send-button"
          data-testid="send-button"
          @click="handleSendMessage"
          :disabled="!messageInput.trim() || isSending"
        >
          <span v-if="isSending" class="send-spinner">⟳</span>
          <span v-else class="send-icon">→</span>
        </button>
      </div>
    </div>
    
    <!-- Smart Action Mode (Step 4 & 5) -->
    <div 
      v-else
      class="action-mode"
      data-testid="action-mode"
    >
      <!-- Chat Icon (only for step 4) -->
      <button 
        v-if="shouldShowChatIcon"
        class="chat-toggle-button"
        data-testid="chat-toggle-button"
        @click="switchToChat"
      >
        <span class="chat-icon">{{ t('transactionalChat.chat.chatMode') }}</span>
      </button>
      
      <!-- Primary Action Button -->
      <button 
        :class="actionButtonClass"
        data-testid="primary-action-button"
        @click="handleActionClick"
      >
        {{ actionButtonText }}
      </button>
    </div>
    
    <!-- Confirmation Modal -->
    <div 
      v-if="showConfirmation"
      class="confirmation-overlay"
      data-testid="confirmation-overlay"
    >
      <div class="confirmation-modal">
        <div class="confirmation-content">
          <h3 class="confirmation-title">
            {{ getConfirmationMessage }}
          </h3>
          <div class="confirmation-buttons">
            <button 
              class="confirmation-button cancel"
              data-testid="confirmation-cancel"
              @click="cancelAction"
            >
              {{ t('transactionalChat.confirmations.no') }}
            </button>
            <button 
              class="confirmation-button confirm"
              data-testid="confirmation-confirm"
              @click="confirmAction"
            >
              {{ t('transactionalChat.confirmations.yes') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dynamic-action-bar {
  background-color: var(--tc-bg-action-bar);
  border-top: 1px solid var(--tc-border-light);
  padding: 12px 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
  transition: all 0.3s ease;
  /* Prevent scroll interference on mobile */
  touch-action: manipulation;
  overscroll-behavior: contain;
}

/* Highlight effect when scrolled to from pinned banner */
.dynamic-action-bar.highlight-action-bar {
  box-shadow: 0 0 0 3px var(--tc-primary), 0 -2px 8px rgba(0, 0, 0, 0.1);
  background-color: var(--tc-primary-suppl);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Chat Mode */
.chat-mode {
  width: 100%;
}

.chat-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  /* Prevent scroll interference on mobile */
  touch-action: manipulation;
  overscroll-behavior: contain;
  /* Ensure container doesn't trigger scroll events */
  overflow: hidden;
}

.chat-input {
  flex: 1;
  background-color: var(--tc-input-bg);
  border: 1px solid var(--tc-input-border);
  border-radius: 24px;
  padding: 12px 16px;
  font-size: 16px;
  color: var(--tc-input-text);
  outline: none;
  transition: border-color 0.2s ease;
  min-height: 48px;
  /* Mobile touch optimizations */
  touch-action: manipulation;
  /* Prevent scroll interference */
  overscroll-behavior: contain;
  /* Ensure input doesn't trigger parent scroll */
  overflow: hidden;
}

.chat-input:focus {
  border-color: var(--tc-input-border-focus);
}

.chat-input::placeholder {
  color: var(--tc-input-placeholder);
}

.chat-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-button {
  background-color: var(--tc-primary);
  color: var(--tc-text-white);
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  background-color: var(--tc-primary-hover);
  transform: scale(1.05);
}

.send-button:active:not(:disabled) {
  background-color: var(--tc-primary-active);
  transform: scale(0.95);
}

.send-button:disabled {
  background-color: var(--tc-border-medium);
  cursor: not-allowed;
  transform: none;
}

.send-icon {
  font-size: 18px;
  font-weight: bold;
}

.send-spinner {
  font-size: 18px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Action Mode */
.action-mode {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.chat-toggle-button {
  background-color: var(--tc-bg-secondary);
  color: var(--tc-text-secondary);
  border: 1px solid var(--tc-border-medium);
  border-radius: 12px;
  padding: 12px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  min-width: 48px;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-toggle-button:hover {
  background-color: var(--tc-border-light);
  transform: scale(1.05);
}

.chat-toggle-button:active {
  transform: scale(0.95);
}

.chat-icon {
  font-size: 18px;
}

.action-button {
  flex: 1;
  background-color: var(--tc-primary);
  color: var(--tc-text-white);
  border: none;
  border-radius: 12px;
  padding: 14px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px;
}

.action-button:hover {
  background-color: var(--tc-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--tc-shadow-md);
}

.action-button:active {
  background-color: var(--tc-primary-active);
  transform: translateY(0);
  box-shadow: var(--tc-shadow);
}

.action-button.danger {
  background-color: var(--tc-warning);
}

.action-button.danger:hover {
  background-color: var(--tc-warning-hover);
}

/* Confirmation Modal */
.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.confirmation-modal {
  background-color: var(--tc-bg-card);
  border-radius: 16px;
  box-shadow: var(--tc-shadow-lg);
  max-width: 400px;
  width: 100%;
  animation: modalSlideUp 0.3s ease-out;
}

@keyframes modalSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.confirmation-content {
  padding: 24px;
  text-align: center;
}

.confirmation-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--tc-text-primary);
  margin: 0 0 24px 0;
  line-height: 1.4;
}

.confirmation-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.confirmation-button {
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px;
  min-width: 80px;
}

.confirmation-button.cancel {
  background-color: var(--tc-bg-secondary);
  color: var(--tc-text-secondary);
  border: 1px solid var(--tc-border-medium);
}

.confirmation-button.cancel:hover {
  background-color: var(--tc-border-light);
}

.confirmation-button.confirm {
  background-color: var(--tc-primary);
  color: var(--tc-text-white);
}

.confirmation-button.confirm:hover {
  background-color: var(--tc-primary-hover);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .dynamic-action-bar {
    padding: 8px 12px;
    /* Enhanced mobile scroll prevention */
    position: sticky;
    bottom: 0;
    /* Prevent any scroll interference */
    overscroll-behavior: none;
    touch-action: manipulation;
  }

  .chat-input-container {
    /* Additional mobile touch handling */
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    /* Prevent scroll bubbling */
    overscroll-behavior: none;
  }

  .chat-input {
    font-size: 16px; /* Prevent zoom on iOS */
    min-height: 44px;
    padding: 10px 14px;
    /* Enhanced mobile input handling */
    -webkit-touch-callout: none;
    /* Prevent scroll interference on mobile */
    overscroll-behavior: none;
    touch-action: manipulation;
  }
  
  .send-button {
    width: 44px;
    height: 44px;
  }
  
  .chat-toggle-button {
    min-width: 44px;
    min-height: 44px;
    padding: 10px;
  }
  
  .action-button {
    padding: 12px 16px;
    font-size: 15px;
    min-height: 44px;
  }
  
  .confirmation-content {
    padding: 20px;
  }
  
  .confirmation-title {
    font-size: 16px;
  }
  
  .confirmation-button {
    padding: 10px 20px;
    font-size: 13px;
    min-height: 40px;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .action-mode {
    gap: 8px;
  }
  
  .chat-toggle-button {
    min-width: 40px;
    min-height: 40px;
    padding: 8px;
  }
  
  .action-button {
    font-size: 14px;
    min-height: 40px;
  }
  
  .confirmation-buttons {
    flex-direction: column;
    gap: 8px;
  }
  
  .confirmation-button {
    width: 100%;
  }
}

/* RTL Support */
[dir="rtl"] .chat-input-container {
  direction: rtl;
}

[dir="rtl"] .action-mode {
  direction: rtl;
}

[dir="rtl"] .send-icon {
  transform: scaleX(-1);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .chat-input {
    border-width: 2px;
  }
  
  .action-button,
  .send-button {
    border: 2px solid var(--tc-primary);
  }
  
  .chat-toggle-button {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .send-button:hover,
  .action-button:hover,
  .chat-toggle-button:hover {
    transform: none;
  }
  
  .confirmation-modal {
    animation: none;
  }
  
  .send-spinner {
    animation: none;
  }
}

/* Focus management for accessibility */
.chat-input:focus,
.send-button:focus,
.action-button:focus,
.chat-toggle-button:focus,
.confirmation-button:focus {
  outline: 2px solid var(--tc-primary);
  outline-offset: 2px;
}
</style>
