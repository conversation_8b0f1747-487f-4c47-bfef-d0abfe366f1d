<script setup lang="ts">
import { computed, ref } from 'vue'
import { NIcon, useMessage } from 'naive-ui'
import { MapPin as NavigationIcon } from '@vicons/tabler'
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore'

import { useTransactionFlowLogic } from '@/composables/useTransactionFlowLogic'
import { useTimerDisplay } from '@/composables/useTimerDisplay'
import { useI18n } from 'vue-i18n'
import { useRtl } from '@/utils/rtl'

const { t } = useI18n()
const message = useMessage()
const transactionalChatStore = useTransactionalChatStore()
const { isRtl } = useRtl()

// RTL state management
// Debug: direction.value, isRtl.value, locale.value

// Timer Logic (using same composable as SmartPaymentSection)
const {
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
} = useTransactionFlowLogic(
  computed(() => transactionalChatStore.chatSessionId || null),
  message
);

// Use shared timer display logic
const {
  timerDisplayValue
} = useTimerDisplay({
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
});

// Timer formatting for status bar (HH:MM only - no seconds for space optimization)
const formatTimeForStatusBar = (timeString: string): string => {
  // timeString comes in format "HH:MM:SS" or "+HH:MM:SS" from useTransactionFlowLogic
  // We need to extract just HH:MM for the status bar
  const isElapsed = timeString.startsWith('+')
  const cleanTime = timeString.replace('+', '')
  const parts = cleanTime.split(':')

  if (parts.length >= 2) {
    const hours = parts[0]
    const minutes = parts[1]
    return `${isElapsed ? '+' : ''}${hours}:${minutes}`
  }

  return timeString // fallback to original if parsing fails
}

// Computed properties

const currentStep = computed(() => transactionalChatStore.currentStep)
const currentStepIndex = computed(() => transactionalChatStore.currentStepIndex)
const totalSteps = computed(() => transactionalChatStore.totalSteps)
const timer = computed(() => transactionalChatStore.timer)
const otherUser = computed(() => transactionalChatStore.otherUser)
const transactionDetails = computed(() => transactionalChatStore.transactionDetails)

// Helper function to format amounts with proper currency formatting
const formatAmount = (amount: string | number, currency: string): string => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  if (currency === 'IRR' && numAmount >= 1000000) {
    const millions = numAmount / 1000000
    return `${millions.toFixed(1)}M ${currency}`
  }
  return `${numAmount.toLocaleString()} ${currency}`
}

const stepTitle = computed(() => {
  const step = currentStep.value
  const otherUserName = otherUser.value?.name || 'User'

  switch (step?.key) {
    case 'paymentInfo':
      return transactionalChatStore.isUsersTurn
        ? t('transactionalChat.steps.paymentInfo')
        : t('transactionalChat.steps.paymentInfoWaiting', { name: otherUserName })

    case 'negotiation':
      return transactionalChatStore.isUsersTurn
        ? t('transactionalChat.steps.negotiation')
        : t('transactionalChat.steps.negotiationWaiting', { name: otherUserName })

    case 'makePayment': {
      if (transactionalChatStore.isUsersTurn) {
        const amount = formatAmount(
          transactionDetails.value?.amountToSend ?? '',
          transactionDetails.value?.currencyFrom || 'CAD'
        )
        return t('transactionalChat.steps.makePayment', { amount })
      } else {
        const amount = formatAmount(
          transactionDetails.value?.amountToReceive ?? '',
          transactionDetails.value?.currencyTo || 'CAD'
        )
        return t('transactionalChat.steps.waitingPayer1WithAmount', { amount })
      }
    }

    case 'confirmReceipt': {
      if (transactionalChatStore.isUsersTurn) {
        const amount = formatAmount(
          transactionDetails.value?.amountToReceive ?? '',
          transactionDetails.value?.currencyTo || 'CAD'
        )
        return t('transactionalChat.steps.confirmReceipt', { amount })
      } else {
        return t('transactionalChat.steps.confirmReceiptWaiting', { name: otherUserName })
      }
    }

    case 'makeSecondPayment': {
      if (transactionalChatStore.isUsersTurn) {
        const amount = formatAmount(
          transactionDetails.value?.amountToSend ?? '',
          transactionDetails.value?.currencyFrom || 'CAD'
        )
        return t('transactionalChat.steps.makeSecondPayment', { amount })
      } else {
        const amount = formatAmount(
          transactionDetails.value?.amountToReceive ?? '',
          transactionDetails.value?.currencyTo || 'CAD'
        )
        return t('transactionalChat.steps.makeSecondPaymentWaitingWithAmount', { amount })
      }
    }

    case 'confirmFirstPaymentReceipt': {
      if (transactionalChatStore.isUsersTurn) {
        const amount = formatAmount(
          transactionDetails.value?.amountToReceive ?? '',
          transactionDetails.value?.currencyTo || 'CAD'
        )
        return t('transactionalChat.steps.confirmFirstPaymentReceipt', { amount })
      } else {
        return t('transactionalChat.steps.confirmFirstPaymentReceiptWaiting', {
          name: otherUserName
        })
      }
    }

    default:
      return step ? t(step.titleKey) : ''
  }
})

// Helper function to format amounts in millions for IRR
const formatAmountForShrunk = (amount: string | number, currency: string): string => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  if (currency === 'IRR' && numAmount >= 1000000) {
    const millions = numAmount / 1000000
    return `${millions.toFixed(1)}M ${currency}`
  }
  return `${numAmount.toLocaleString()} ${currency}`
}

// Optimized title for shrunk mode (shorter, no "اقدام شما")
const shrunkStepTitle = computed(() => {
  const step = currentStep.value
  const otherUserName = otherUser.value?.name || 'User'

  switch (step?.key) {
    case 'paymentInfo':
      return transactionalChatStore.isUsersTurn
        ? t('transactionalChat.steps.paymentInfoShort')
        : t('transactionalChat.steps.paymentInfoWaitingShort', { name: otherUserName })

    case 'negotiation':
      return transactionalChatStore.isUsersTurn
        ? t('transactionalChat.steps.negotiationShort')
        : t('transactionalChat.steps.negotiationWaitingShort', { name: otherUserName })

    case 'makePayment': {
      if (transactionalChatStore.isUsersTurn) {
        const amount = formatAmountForShrunk(
          transactionDetails.value?.amountToSend ?? '',
          transactionDetails.value?.currencyFrom || 'CAD'
        )
        return t('transactionalChat.steps.makePaymentShort', { amount })
      } else {
        return t('transactionalChat.steps.makeSecondPaymentWaitingShort')
      }
    }

    case 'confirmReceipt':
      return transactionalChatStore.isUsersTurn
        ? t('transactionalChat.steps.confirmReceiptShort')
        : t('transactionalChat.steps.confirmReceiptWaitingShort', { name: otherUserName })

    case 'makeSecondPayment': {
      if (transactionalChatStore.isUsersTurn) {
        const amount = formatAmountForShrunk(
          transactionDetails.value?.amountToSend ?? '',
          transactionDetails.value?.currencyFrom || 'CAD'
        )
        return t('transactionalChat.steps.makeSecondPaymentShort', { amount })
      } else {
        return t('transactionalChat.steps.makeSecondPaymentWaitingShort')
      }
    }

    case 'confirmFirstPaymentReceipt':
      return transactionalChatStore.isUsersTurn
        ? t('transactionalChat.steps.confirmFirstPaymentReceiptShort')
        : t('transactionalChat.steps.confirmFirstPaymentReceiptWaitingShort', {
            name: otherUserName
          })

    default:
      // Fallback to regular title but try to make it shorter
      return step ? t(step.titleKey) : ''
  }
})

// Use the timer display from composable, formatted for status bar (HH:MM only)
const timerText = computed(() => {
  if (!timerDisplayValue.value) return ''
  return formatTimeForStatusBar(timerDisplayValue.value)
})

// Use timer color from composable
const timerColor = computed(() => {
  if (!timerDisplayValue.value) return 'var(--tc-text-secondary)'

  if (isTimerCritical.value) {
    return 'var(--tc-timer-danger)'
  } else if (isTimerExpired.value) {
    return 'var(--tc-timer-danger)'
  } else {
    return 'var(--tc-timer-normal)'
  }
})



const isUsersTurn = computed(() => transactionalChatStore.isUsersTurn)
const isStatusBarShrunk = computed(() => transactionalChatStore.isStatusBarShrunk)

// Interactive navigation functionality
const pinnedAction = computed(() => transactionalChatStore.pinnedAction)
const isActionCardVisible = computed(() => transactionalChatStore.isActionCardVisible)
const shouldShowNavigationHint = computed(() => {
  return pinnedAction.value !== null && !isActionCardVisible.value
})

// Steps that require a timer in shrunk mode
const stepsWithTimer = ['makePayment', 'confirmReceipt', 'makeSecondPayment', 'confirmFirstPaymentReceipt']

const showShrunkTimer = computed(() => {
  const shrunk = isStatusBarShrunk.value
  const timerActive = !!timerDisplayValue.value
  const stepKey = currentStep.value?.key
  const show = shrunk && timerActive && stepsWithTimer.includes(stepKey)

  // Debug logging to help troubleshoot timer display issues (heavily throttled)
  if (process.env.NODE_ENV === 'development' && Math.random() < 0.01) {
    console.log('🔍 [SmartStatusBar] Timer Debug:', {
      shrunk,
      timerActive,
      timerDisplayValue: timerDisplayValue.value,
      stepKey,
      show
    })
  }

  return show
})

// Enhanced navigation functionality
const isNavigationActive = ref(false)

// Handle click on status bar to scroll to action
const handleStatusBarClick = async () => {
  if (shouldShowNavigationHint.value && pinnedAction.value) {
    isNavigationActive.value = true

    try {
      // Use the enhanced scroll system for better navigation
      await transactionalChatStore.scrollToActionCard(pinnedAction.value.cardId)

      // Add a brief visual feedback
      setTimeout(() => {
        isNavigationActive.value = false
      }, 1000)
    } catch (error) {
      console.error('Failed to scroll to action card:', error)
      isNavigationActive.value = false
    }
  }
}

// Enhanced navigation hint with better UX
const navigationHintClass = computed(() => {
  return {
    'navigation-hint': true,
    'navigation-hint--active': isNavigationActive.value,
    'navigation-hint--pulse': shouldShowNavigationHint.value && !isNavigationActive.value
  }
})



</script>

<template>
  <div
    class="smart-status-bar"
    :class="[
      isStatusBarShrunk ? 'shrunk' : 'expanded',
      { 'rtl-layout': isRtl }
    ]"
    data-testid="smart-status-bar"
  >
    <!-- Shrunk (compact) mode -->
  <template v-if="isStatusBarShrunk">
      <div
        class="shrunk-bar"
        :class="{
          'shrunk-bar--interactive': shouldShowNavigationHint,
          'shrunk-bar--glowing': shouldShowNavigationHint,
          'shrunk-bar--rtl': isRtl
        }"
        @click="handleStatusBarClick"
      >
        <!-- Timer (always first in DOM, positioned via CSS) -->
        <span
          v-if="showShrunkTimer"
          class="shrunk-timer"
          :class="{
            'shrunk-timer--rtl': isRtl,
            'shrunk-timer--ltr': !isRtl
          }"
          :style="{ color: timerColor }"
          :data-debug="isRtl ? 'rtl-timer' : 'ltr-timer'"
        >
          <template v-if="isRtl">{{ timerText }} •</template>
          <template v-else>• {{ timerText }}</template>
        </span>

        <!-- Content (always after timer in DOM, positioned via CSS) -->
        <div
          class="shrunk-content"
          :class="{
            'shrunk-content--rtl': isRtl,
            'shrunk-content--ltr': !isRtl
          }"
          :data-debug="isRtl ? 'rtl-content' : 'ltr-content'"
        >
          <span class="shrunk-step">
            {{ t('transactionalChat.statusBar.stepShort', { current: currentStepIndex + 1 }) }}
          </span>
          <span
            class="shrunk-title"
            :class="{ 'shrunk-title--clickable': shouldShowNavigationHint }"
          >
            {{ shrunkStepTitle }}
          </span>
        </div>

        <!-- Navigation hint icon -->
        <div
          v-if="shouldShowNavigationHint"
          :class="navigationHintClass"
          :title="t('transactionalChat.statusBar.clickToScrollToAction')"
        >
          <n-icon :component="NavigationIcon" />
        </div>
      </div>
    </template>
    <!-- Expanded (full) mode -->
    <template v-else>
      <!-- Progress Steps -->
      <div
        class="progress-container"
        data-testid="progress-container"
      >
        <div class="progress-steps">
          <div
            v-for="stepIndex in totalSteps"
            :key="stepIndex"
            class="progress-step"
            :class="{
              'step-completed': stepIndex <= currentStepIndex,
              'step-active': stepIndex === currentStepIndex + 1,
              'step-pending': stepIndex > currentStepIndex + 1
            }"
            :data-testid="`progress-step-${stepIndex}`"
          >
            <div class="step-circle">
              <span v-if="stepIndex <= currentStepIndex" class="step-check">✓</span>
              <span v-else class="step-number">{{ stepIndex }}</span>
            </div>
            <div
              v-if="stepIndex < totalSteps"
              class="step-line"
              :class="{
                'line-completed': stepIndex < currentStepIndex,
                'line-active': stepIndex === currentStepIndex,
                'line-pending': stepIndex > currentStepIndex
              }"
            ></div>
          </div>
        </div>
      </div>
      <!-- Status Info -->
      <div
        class="status-info"
        data-testid="status-info"
      >
        <!-- Main Status Text -->
        <div
          class="status-main"
          :class="{ 'status-main--interactive': shouldShowNavigationHint }"
          @click="handleStatusBarClick"
        >
          <h2
            class="status-title"
            :class="{
              'user-turn': isUsersTurn,
              'status-title--clickable': shouldShowNavigationHint
            }"
            data-testid="status-title"
          >
            {{ stepTitle }}
            <!-- Navigation hint icon for expanded mode -->
            <n-icon
              v-if="shouldShowNavigationHint"
              :component="NavigationIcon"
              class="navigation-hint-inline"
              :title="t('transactionalChat.statusBar.clickToScrollToAction')"
            />
          </h2>
          <!-- Step Counter -->
          <p
            class="step-counter"
            data-testid="step-counter"
          >
            {{ t('transactionalChat.statusBar.step', {
              current: currentStepIndex + 1,
              total: totalSteps
            }) }}
          </p>
        </div>
        <!-- Timer (if active) -->
        <div
          v-if="timerDisplayValue"
          class="timer-container"
          :style="{ color: timerColor }"
          data-testid="timer-container"
        >
          <span class="timer-text">{{ timerText }}</span>
          <div
            class="timer-pulse"
            :class="{
              'pulse-danger': timer.remainingSeconds <= 60,
              'pulse-warning': timer.remainingSeconds <= 300 && timer.remainingSeconds > 60,
              'pulse-normal': timer.remainingSeconds > 300
            }"
          ></div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped>

/* Flat, non-glassmorphic status bar */
.smart-status-bar {
  background: var(--tc-bg-status, #f1f5f9);
  border-bottom: 1px solid var(--tc-border-light, #e2e8f0);
  box-shadow: 0 1px 3px 0 rgba(0,0,0,0.04);
  transition: all 0.25s cubic-bezier(0.4,0,0.2,1);
  color: var(--tc-text-primary, #1e293b);
  position: sticky;
  top: 0;
  z-index: 10;
  /* Prevent layout shift during initialization */
  contain: layout style;
  /* Ensure consistent height to prevent flashing */
  min-height: 60px;
}

/* Expanded mode specific styles */
.smart-status-bar.expanded {
  padding: 16px;
}

/* Shrunk mode specific styles */
.smart-status-bar.shrunk {
  padding: 6px 14px;
  min-height: 36px;
  display: flex;
  align-items: center;
  font-size: 14px;
}

/* Flat dark theme */
[data-theme="dark"] .smart-status-bar {
  background: var(--tc-bg-status, #1e293b);
  border-bottom: 1px solid var(--tc-border-dark, #94a3b8);
  box-shadow: 0 1px 3px 0 rgba(0,0,0,0.08);
  color: var(--tc-text-primary, #f7fafc);
}

/* Flat light theme */
[data-theme="light"] .smart-status-bar {
  background: var(--tc-bg-status, #f1f5f9);
  border-bottom: 1px solid var(--tc-border-light, #e2e8f0);
  box-shadow: 0 1px 3px 0 rgba(0,0,0,0.04);
  color: var(--tc-text-primary, #1e293b);
}

.shrunk-bar {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 4px 8px;
  margin: -4px -8px;
}

/* RTL-specific layout */
.shrunk-bar--rtl {
  flex-direction: row-reverse; /* Reverse the order for RTL */
}

.shrunk-bar--interactive {
  cursor: pointer;
  background: rgba(var(--primary-color-rgb), 0.05);
}

.shrunk-bar--interactive:hover {
  background: rgba(var(--primary-color-rgb), 0.1);
  transform: translateY(-1px);
}

.shrunk-bar--glowing {
  animation: statusBarGlow 2s ease-in-out infinite;
  box-shadow: 0 0 0 1px rgba(var(--primary-color-rgb), 0.3);
}


.shrunk-step {
  font-weight: 600;
  color: var(--tc-primary, #3b82f6);
  margin-right: 4px;
}


/* Ensure step text is visible in both themes */
[data-theme="dark"] .shrunk-step {
  color: var(--tc-primary, #9f7aea);
}

[data-theme="light"] .shrunk-step {
  color: var(--tc-primary, #3b82f6);
}

.shrunk-title {
  font-weight: 500;
  color: var(--tc-text-primary, #1e293b);
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60vw;
  transition: color 0.3s ease;
}


.shrunk-title--clickable {
  color: var(--tc-primary, #3b82f6);
  font-weight: 600;
}


/* Theme-specific adjustments */
[data-theme="dark"] .shrunk-title {
  color: var(--tc-text-primary, #f7fafc);
}

[data-theme="light"] .shrunk-title {
  color: var(--tc-text-primary, #1e293b);
}

[data-theme="dark"] .shrunk-title--clickable {
  color: var(--tc-primary, #9f7aea);
}

[data-theme="light"] .shrunk-title--clickable {
  color: var(--tc-primary, #3b82f6);
}

.shrunk-timer {
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.85); /* White timer text for glassmorphism */
  flex-shrink: 0; /* Prevent timer from shrinking */
}

/* Timer positioning */
.shrunk-timer--rtl {
  /* In RTL with row-reverse, timer appears first (left side) */
  order: 0;
}

.shrunk-timer--ltr {
  /* In LTR, timer appears last (right side) */
  order: 1;
}

/* Content positioning and direction */
.shrunk-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
  /* Default order 0, will be positioned based on flex-direction */
}

.shrunk-content--rtl {
  direction: rtl;
  text-align: right;
}

.shrunk-content--ltr {
  direction: ltr;
  text-align: left;
}

/* Theme-specific timer colors */
[data-theme="dark"] .shrunk-timer {
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="light"] .shrunk-timer {
  color: rgba(30, 41, 59, 0.8); /* Dark text for light theme */
}

/* Timer color will be overridden by inline styles from timerColor computed */

.navigation-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: rgba(96, 165, 250, 0.9);
  opacity: 0.8;
  transition: all 0.3s ease;
  cursor: pointer;
}

.navigation-hint--pulse {
  animation: navigationPulse 1.5s ease-in-out infinite;
}

.navigation-hint--active {
  color: rgba(34, 197, 94, 0.9); /* Green when active */
  transform: scale(1.1);
}

.navigation-hint:hover {
  opacity: 1;
  color: rgba(96, 165, 250, 1);
  transform: scale(1.05);
}

/* Progress Steps */
.progress-container {
  margin-bottom: 16px;
}

.progress-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  max-width: 100%;
  overflow-x: auto;
  padding: 0 4px;
}

.progress-step {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  min-width: 0;
}

.step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  flex-shrink: 0;
  z-index: 2;
  position: relative;
}

/* Step states */
.step-completed .step-circle {
  background-color: var(--tc-step-complete);
  color: var(--tc-text-white);
}

.step-active .step-circle {
  background-color: var(--tc-step-active);
  color: var(--tc-text-white);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
}

.step-pending .step-circle {
  background-color: var(--tc-step-pending);
  color: var(--tc-text-muted);
}

.step-check {
  font-size: 16px;
}

.step-number {
  font-size: 12px;
}

/* Step lines */
.step-line {
  height: 2px;
  flex: 1;
  margin: 0 8px;
  transition: background-color 0.3s ease;
}

.line-completed {
  background-color: var(--tc-step-complete);
}

.line-active {
  background: linear-gradient(to right, var(--tc-step-complete), var(--tc-step-active));
}

.line-pending {
  background-color: var(--tc-step-line);
}

/* Status Info */
.status-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.status-main {
  flex: 1;
  min-width: 0;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 8px;
  margin: -8px;
}

.status-main--interactive {
  cursor: pointer;
  background: rgba(var(--primary-color-rgb), 0.05);
}

.status-main--interactive:hover {
  background: rgba(var(--primary-color-rgb), 0.1);
  transform: translateY(-1px);
}

.status-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--tc-text-primary, #1e293b);
  line-height: 1.4;
  word-wrap: break-word;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: color 0.3s ease;
}

.status-title.user-turn {
  color: var(--tc-primary, #3b82f6);
}

.status-title--clickable {
  color: var(--primary-color);
}

.navigation-hint-inline {
  color: var(--primary-color);
  opacity: 0.8;
  animation: pulse 1.5s ease-in-out infinite;
}

.navigation-hint-inline:hover {
  opacity: 1;
}

.step-counter {
  font-size: 12px;
  color: var(--tc-text-secondary, #64748b);
  margin: 0;
}

/* Timer */
.timer-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.timer-text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.timer-pulse {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 0.8;
}

.pulse-normal {
  background-color: var(--tc-timer-normal);
  animation: pulse-normal 2s infinite;
}

.pulse-warning {
  background-color: var(--tc-timer-warning);
  animation: pulse-warning 1.5s infinite;
}

.pulse-danger {
  background-color: var(--tc-timer-danger);
  animation: pulse-danger 1s infinite;
}

@keyframes pulse-normal {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 1; }
}

@keyframes pulse-warning {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

@keyframes pulse-danger {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .smart-status-bar {
    padding: 12px;
  }
  
  .progress-steps {
    padding: 0 2px;
  }
  
  .step-circle {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .step-check {
    font-size: 14px;
  }
  
  .step-number {
    font-size: 11px;
  }
  
  .status-title {
    font-size: 15px;
  }
  
  .step-counter {
    font-size: 11px;
  }
  
  .timer-text {
    font-size: 13px;
  }
  
  .status-info {
    gap: 12px;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .step-line {
    margin: 0 4px;
  }
  
  .step-circle {
    width: 24px;
    height: 24px;
    font-size: 11px;
  }
  
  .step-check {
    font-size: 12px;
  }
  
  .step-number {
    font-size: 10px;
  }
}

/* RTL Support */
[dir="rtl"] .progress-steps {
  direction: rtl;
}

[dir="rtl"] .status-info {
  direction: rtl;
}

[dir="rtl"] .status-main {
  text-align: right;
}

[dir="rtl"] .timer-container {
  flex-direction: row-reverse;
}

/* Keyframe animations */
@keyframes statusBarGlow {
  0%, 100% {
    box-shadow: 0 0 0 1px rgba(var(--primary-color-rgb), 0.3);
  }
  50% {
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.5),
                0 0 8px rgba(var(--primary-color-rgb), 0.3);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes navigationPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.15);
    color: rgba(96, 165, 250, 1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .shrunk-bar--glowing,
  .navigation-hint,
  .navigation-hint-inline {
    animation: none;
  }

  .shrunk-bar--interactive:hover,
  .status-main--interactive:hover {
    transform: none;
  }
}
</style>
