<script setup lang="ts">
import { onMounted, onUnmounted, watch, ref, nextTick, computed } from 'vue'
import ConfettiExplosion from 'vue-confetti-explosion'
import { useRoute } from 'vue-router'
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore'
import { useTransactionStore } from '@/stores/transaction';
import { TransactionStatusEnum } from '@/types/transaction'
import TheHeaderBar from '@/components/TransactionalChat/TheHeaderBar.vue'
import TheSmartStatusBar from '@/components/TransactionalChat/TheSmartStatusBar.vue'
import TheUnifiedFeed from '@/components/TransactionalChat/TheUnifiedFeed.vue'
import TheDynamicActionBar from '@/components/TransactionalChat/TheDynamicActionBar.vue'
import TransactionalChatPaymentMethodSelector from '@/components/TransactionalChatPaymentMethodSelector.vue'

const route = useRoute()
const transactionalChatStore = useTransactionalChatStore()
const transactionStore = useTransactionStore()

const showConfetti = ref(false)
// Reactive window dimensions for confetti responsiveness
const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1200)
const windowHeight = ref(typeof window !== 'undefined' ? window.innerHeight : 800)

// For status bar shrink on scroll (temporary fallback)
import { onBeforeUnmount } from 'vue'
const unifiedFeedRef = ref<any>(null)

// Enhanced scroll handling with mobile fixes and flashing prevention
let lastScrollTop = 0
let isInitialized = false
let scrollTimeout: number | null = null
const SHRINK_SCROLL_THRESHOLD = 80
const EXPAND_SCROLL_THRESHOLD = 40
const SCROLL_DEBOUNCE_DELAY = 16 // ~60fps

function handleFeedScroll() {
  const feedComponent = unifiedFeedRef.value
  const scrollEl = feedComponent?.feedContainer
  if (!scrollEl || !isInitialized) return

  // Debounce scroll events for better performance
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }

  scrollTimeout = setTimeout(() => {
    const scrollTop = scrollEl.scrollTop
    const scrollHeight = scrollEl.scrollHeight
    const clientHeight = scrollEl.clientHeight
    const scrollDirection = scrollTop > lastScrollTop ? 'down' : 'up'

    // Prevent status bar changes when at the very bottom to avoid layout issues
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10

    if (!isAtBottom) {
      if (scrollDirection === 'down' && scrollTop > SHRINK_SCROLL_THRESHOLD) {
        transactionalChatStore.isStatusBarShrunk = true
      } else if (scrollDirection === 'up' && scrollTop < EXPAND_SCROLL_THRESHOLD) {
        transactionalChatStore.isStatusBarShrunk = false
      }
    }

    lastScrollTop = scrollTop
  }, SCROLL_DEBOUNCE_DELAY)
}

// Window resize handler for confetti responsiveness
const updateWindowDimensions = () => {
  if (typeof window !== 'undefined') {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
  }
}

// This function will be called directly when the transaction is completed.
const handleTransactionCompleted = async () => {
  console.log('🎉 Confetti triggered by transaction completion!');
  // Use nextTick to ensure the v-if directive re-triggers the component animation
  showConfetti.value = false;
  await nextTick();
  showConfetti.value = true;

  // Automatically hide confetti after animation completes
  setTimeout(() => {
    showConfetti.value = false;
  }, 3500); // Slightly longer than the animation duration
};

// Track completed transactions to prevent duplicate confetti
const completedTransactions = new Set<string>()

// Watch for transaction status changes to trigger confetti
watch(
  () => transactionalChatStore.transactionStatus,
  (newStatus, oldStatus) => {
    console.log('🔍 [Confetti] Transaction status changed:', {
      oldStatus,
      newStatus,
      isCompleted: newStatus === TransactionStatusEnum.COMPLETED,
      completedEnum: TransactionStatusEnum.COMPLETED,
      transactionId: transactionalChatStore.currentTransactionIdRef
    })

    if (newStatus === TransactionStatusEnum.COMPLETED &&
        oldStatus !== TransactionStatusEnum.COMPLETED &&
        transactionalChatStore.currentTransactionIdRef) {

      const transactionId = transactionalChatStore.currentTransactionIdRef

      // Prevent duplicate confetti for the same transaction
      if (!completedTransactions.has(transactionId)) {
        completedTransactions.add(transactionId)
        console.log('🎉 Transaction completed! Triggering confetti for transaction:', transactionId)
        handleTransactionCompleted()
      }
    }
  }
)

// Get transaction ID from route
const transactionId = route.params.transactionId as string

// Handle initial mount


onMounted(async () => {
  // Clear both stores to prevent data leakage between transactions
  transactionalChatStore.reset()
  transactionStore.clearTransaction()
  // Clear completed transactions set for new transaction
  completedTransactions.clear()

  // Add window resize listener for confetti responsiveness
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', updateWindowDimensions)
    updateWindowDimensions() // Set initial dimensions
  }

  // Initialize the transactional chat state
  if (transactionId) {
    await transactionalChatStore.fetchTransaction(transactionId)
  }

  // Initialize enhanced scroll handling with proper initialization
  await nextTick()
  const feedComponent = unifiedFeedRef.value
  const scrollEl = feedComponent?.feedContainer
  if (scrollEl) {
    // Initialize status bar state based on current scroll position
    const initialScrollTop = scrollEl.scrollTop
    transactionalChatStore.isStatusBarShrunk = initialScrollTop > SHRINK_SCROLL_THRESHOLD
    lastScrollTop = initialScrollTop

    // Add scroll listener
    scrollEl.addEventListener('scroll', handleFeedScroll, { passive: true })

    // Mark as initialized to prevent flashing
    isInitialized = true
  }
})


onBeforeUnmount(() => {
  // Clean up scroll handling
  isInitialized = false
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
    scrollTimeout = null
  }

  const feedComponent = unifiedFeedRef.value
  const scrollEl = feedComponent?.feedContainer
  if (scrollEl) {
    scrollEl.removeEventListener('scroll', handleFeedScroll)
  }
})

// Watch for route parameter changes (navigation between transactions)
watch(
  () => route.params.transactionId,
  async (newTransactionId, oldTransactionId) => {
    if (newTransactionId && newTransactionId !== oldTransactionId) {
      // Clear both stores to prevent data leakage between transactions
      transactionalChatStore.reset()
      transactionStore.clearTransaction()
      // Clear completed transactions set for new transaction
      completedTransactions.clear()

      // Load new transaction
      await transactionalChatStore.fetchTransaction(newTransactionId as string)
    }
  }
)

onUnmounted(() => {
  // Clean up any timers or subscriptions in both stores
  transactionalChatStore.stopTimer()
  transactionalChatStore.reset()
  transactionStore.clearTransaction()
  // Clear completed transactions set
  completedTransactions.clear()

  // Reset scroll state (enhanced cleanup)
  isInitialized = false
  lastScrollTop = 0
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
    scrollTimeout = null
  }

  // Remove window resize listener
  if (typeof window !== 'undefined') {
    window.removeEventListener('resize', updateWindowDimensions)
  }
})
</script>

<template>
  <div 
    class="transactional-chat-container"
    data-testid="transaction-view"
  >
    <div class="confetti-container">
      <ConfettiExplosion
        v-if="showConfetti"
        :particle-count="150"
        :force="0.3"
        :duration="3000"
        :colors="['#18a058', '#2080f0', '#f0a020', '#d03050', '#9c26b0', '#ff6b35']"
        :particle-size="8"
        :stage-width="windowWidth"
        :stage-height="windowHeight"
        :should-destroy-after-done="true"
      />
    </div>
    <!-- Header Bar -->
    <TheHeaderBar data-testid="header-bar" />
    
    <!-- Smart Status Bar (Enhanced with Navigation) -->
    <TheSmartStatusBar data-testid="status-bar" />

    <!-- Unified Feed -->

    <TheUnifiedFeed
      ref="unifiedFeedRef"
      class="unified-feed"
      data-testid="unified-feed"
    />
    
    <!-- Dynamic Action Bar -->
    <TheDynamicActionBar data-testid="action-bar" />
    
    <!-- Payment Method Selector Modal -->
    <TransactionalChatPaymentMethodSelector data-testid="payment-method-selector" />
    
    <!-- Loading Overlay -->
    <div 
      v-if="transactionalChatStore.isLoading"
      class="loading-overlay"
      data-testid="loading-overlay"
    >
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p class="loading-text">{{ $t('common.loading') }}...</p>
      </div>
    </div>
    
    <!-- Error State -->
    <div 
      v-if="transactionalChatStore.errorState.hasError"
      class="error-overlay"
      data-testid="error-overlay"
    >
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <h3 class="error-title">{{ $t('transactionalChat.errors.loadingTransaction') }}</h3>
        <p class="error-message">{{ transactionalChatStore.errorState.message }}</p>
        <button 
          class="error-retry-btn"
          data-testid="retry-btn"
          @click="transactionalChatStore.fetchTransaction(transactionId)"
        >
          {{ $t('common.retry') }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.transactional-chat-container {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  /* Use Naive UI theme variables */
  background: var(--n-body-color);
  color: var(--n-text-color1);
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.confetti-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1px;
  height: 1px;
  z-index: 9999;
  pointer-events: none; /* Allow clicks to pass through */
  /* Ensure confetti works in both light and dark themes */
  mix-blend-mode: normal;
}

.unified-feed {
  flex: 1;
  /* Allow scrolling in the feed */
  overflow-y: auto;
  overflow-x: hidden;
}

/* --- Status Bar Animation --- */

.status-bar {
  transition:
    height 0.44s cubic-bezier(0.22, 1, 0.36, 1),
    padding 0.44s cubic-bezier(0.22, 1, 0.36, 1),
    opacity 0.32s cubic-bezier(0.22, 1, 0.36, 1),
    box-shadow 0.44s cubic-bezier(0.22, 1, 0.36, 1),
    background-color 0.44s cubic-bezier(0.22, 1, 0.36, 1),
    border-radius 0.44s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: height, padding, opacity, box-shadow, background-color, border-radius;
  overflow: hidden;
  background: var(--tc-bg-header);
  box-shadow: var(--tc-shadow-sm);
  border-radius: 18px;
}
.status-bar--shrunk {
  height: 44px !important;
  min-height: 44px !important;
  max-height: 48px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  padding-left: 18px !important;
  padding-right: 18px !important;
  opacity: 0.98;
  box-shadow: 0 2px 12px 0 var(--shadow-light), 0 1.5px 4px 0 var(--shadow-medium);
  background: linear-gradient(90deg, rgba(255,255,255,0.85) 60%, rgba(245,245,255,0.7) 100%);
  border-radius: 18px 18px 12px 12px / 18px 18px 12px 12px;
  backdrop-filter: blur(12px) saturate(1.2);
  -webkit-backdrop-filter: blur(12px) saturate(1.2);
  border: 1px solid var(--tc-border-light);
  color: var(--tc-text-primary);
  transition: all 0.44s cubic-bezier(0.22, 1, 0.36, 1);
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Inner compact bar improvements */
.shrunk-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  min-width: 0;
  overflow: hidden;
}
.shrunk-step {
  font-weight: 600;
  color: var(--tc-primary);
  font-size: 15px;
  letter-spacing: 0.01em;
  white-space: nowrap;
  margin-inline-end: 6px;
}
.shrunk-title {
  font-weight: 500;
  color: var(--tc-text-primary);
  font-size: 15px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 60vw;
}
.status-bar--expanded {
  height: 88px !important;
  min-height: 64px !important;
  max-height: 120px !important;
  padding-top: 12px !important;
  padding-bottom: 12px !important;
  opacity: 1;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10), 0 1.5px 4px 0 rgba(0,0,0,0.08);
  background: linear-gradient(90deg, var(--tc-bg-header), var(--tc-bg-card));
  border-radius: 24px;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.loading-spinner {
  background: var(--n-card-color);
  padding: 32px;
  border-radius: 16px;
  text-align: center;
  box-shadow: var(--n-box-shadow2);
  border: 1px solid var(--n-border-color);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--n-border-color);
  border-top: 3px solid var(--n-primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loading-text {
  color: var(--n-text-color2);
  font-size: 14px;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Overlay */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.error-content {
  background: var(--n-card-color);
  padding: 32px;
  border-radius: 16px;
  text-align: center;
  max-width: 400px;
  width: 100%;
  box-shadow: var(--n-box-shadow2);
  border: 1px solid var(--n-border-color);
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-title {
  color: var(--n-text-color1);
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.error-message {
  color: var(--n-text-color2);
  font-size: 14px;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.error-retry-btn {
  background: var(--n-primary-color);
  color: var(--n-text-color-base);
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px; /* Touch target */
  box-shadow: var(--n-box-shadow1);
}

.error-retry-btn:hover {
  background: var(--n-primary-color-hover);
  transform: translateY(-1px);
  box-shadow: var(--n-box-shadow2);
}

.error-retry-btn:active {
  background: var(--n-primary-color-pressed);
  transform: translateY(0);
  box-shadow: var(--n-box-shadow1);
}

/* Mobile-first responsive design */
@media (max-width: 768px) {
  .transactional-chat-container {
    width: 100vw;
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
    margin: 0;
    padding: 0;
    /* Prevent overscroll and layout issues on mobile */
    overscroll-behavior: none;
    /* Ensure proper flex layout on mobile */
    display: flex;
    flex-direction: column;
  }

  .unified-feed {
    /* Ensure feed takes available space properly on mobile */
    flex: 1;
    min-height: 0; /* Allow flex item to shrink below content size */
    /* Prevent layout shifts when status bar changes */
    contain: layout style;
    /* Ensure scroll is contained within the feed */
    overscroll-behavior: contain;
    touch-action: pan-y; /* Allow only vertical scrolling */
  }

  .loading-spinner,
  .error-content {
    padding: 24px;
    margin: 16px;
  }

  .error-title {
    font-size: 16px;
  }

  .error-message {
    font-size: 13px;
  }
}

/* RTL Support */
[dir="rtl"] .loading-spinner,
[dir="rtl"] .error-content {
  text-align: center; /* Center alignment works for both LTR and RTL */
}
</style>